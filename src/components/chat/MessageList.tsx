import React, { useEffect, useRef } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "framer-motion";

interface Message {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
  status?: "complete" | "streaming" | "thinking";
  timestamp: Date;
}

interface ToolCall {
  id: string;
  tool: string;
  input: Record<string, any>;
  output?: Record<string, any>;
  status: "pending" | "complete" | "error";
}

interface MessageListProps {
  messages?: Message[];
  toolCalls?: ToolCall[];
  isThinking?: boolean;
  thinkingText?: string;
  streamingContent?: string;
}

const MessageList: React.FC<MessageListProps> = ({
  messages = [],
  toolCalls = [],
  isThinking = false,
  thinkingText = "Thinking...",
  streamingContent = "",
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Scroll to bottom when messages change
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, isThinking]);

  const renderMessageContent = (content: string) => {
    // Simple markdown-like rendering for code blocks
    const parts = content.split(/(```[\s\S]*?```)/g);

    return parts.map((part, index) => {
      if (part.startsWith("```") && part.endsWith("```")) {
        const code = part.slice(3, -3);
        return (
          <pre
            key={index}
            className="bg-muted p-4 rounded-md overflow-x-auto my-2"
          >
            <code>{code}</code>
          </pre>
        );
      }
      return (
        <p key={index} className="whitespace-pre-wrap">
          {part}
        </p>
      );
    });
  };

  const renderThinkingIndicator = () => {
    return (
      <motion.div
        className="flex items-center gap-2 text-muted-foreground"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex space-x-1">
          <motion.div
            className="h-2 w-2 rounded-full bg-primary"
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 1, repeat: Infinity }}
          />
          <motion.div
            className="h-2 w-2 rounded-full bg-primary"
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 1, repeat: Infinity, delay: 0.2 }}
          />
          <motion.div
            className="h-2 w-2 rounded-full bg-primary"
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 1, repeat: Infinity, delay: 0.4 }}
          />
        </div>
        <span>{thinkingText}</span>
      </motion.div>
    );
  };

  const renderToolCall = (toolCall: ToolCall) => {
    return (
      <Card key={toolCall.id} className="p-4 my-2 bg-muted/50">
        <div className="font-medium">Tool Call: {toolCall.tool}</div>
        <div className="mt-2">
          <div className="text-sm text-muted-foreground">Input:</div>
          <pre className="bg-muted p-2 rounded-md text-xs overflow-x-auto mt-1">
            {JSON.stringify(toolCall.input, null, 2)}
          </pre>
        </div>
        {toolCall.output && (
          <div className="mt-2">
            <div className="text-sm text-muted-foreground">Output:</div>
            <pre className="bg-muted p-2 rounded-md text-xs overflow-x-auto mt-1">
              {JSON.stringify(toolCall.output, null, 2)}
            </pre>
          </div>
        )}
        {toolCall.status === "pending" && (
          <div className="mt-2 flex items-center gap-2">
            <Skeleton className="h-4 w-4 rounded-full" />
            <span className="text-sm text-muted-foreground">Processing...</span>
          </div>
        )}
        {toolCall.status === "error" && (
          <div className="mt-2 text-sm text-destructive">
            Error processing tool call
          </div>
        )}
      </Card>
    );
  };

  return (
    <div className="flex flex-col space-y-6 py-4 bg-background">
      {messages.map((message) => (
        <div
          key={message.id}
          className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
        >
          <div
            className={`flex gap-3 max-w-[80%] ${message.role === "user" ? "flex-row-reverse" : "flex-row"}`}
          >
            <Avatar className="h-8 w-8">
              {message.role === "user" ? (
                <>
                  <AvatarImage src="https://api.dicebear.com/7.x/avataaars/svg?seed=user" />
                  <AvatarFallback>U</AvatarFallback>
                </>
              ) : (
                <>
                  <AvatarImage src="https://api.dicebear.com/7.x/bottts/svg?seed=assistant" />
                  <AvatarFallback>AI</AvatarFallback>
                </>
              )}
            </Avatar>
            <div
              className={`rounded-lg p-4 ${
                message.role === "user"
                  ? "bg-primary text-primary-foreground"
                  : "bg-muted"
              }`}
            >
              {message.status === "streaming" ? (
                <>
                  {renderMessageContent(message.content)}
                  <motion.span
                    className="inline-block w-1.5 h-4 bg-current ml-0.5"
                    animate={{ opacity: [1, 0, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                  />
                </>
              ) : (
                renderMessageContent(message.content)
              )}
            </div>
          </div>
        </div>
      ))}

      {toolCalls.map(renderToolCall)}

      {streamingContent && (
        <div className="flex justify-start">
          <div className="flex gap-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src="https://api.dicebear.com/7.x/bottts/svg?seed=assistant" />
              <AvatarFallback>AI</AvatarFallback>
            </Avatar>
            <div className="rounded-lg p-4 bg-muted">
              {renderMessageContent(streamingContent)}
              <motion.span
                className="inline-block w-1.5 h-4 bg-current ml-0.5"
                animate={{ opacity: [1, 0, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
              />
            </div>
          </div>
        </div>
      )}

      {isThinking && !streamingContent && (
        <div className="flex justify-start">
          <div className="flex gap-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src="https://api.dicebear.com/7.x/bottts/svg?seed=assistant" />
              <AvatarFallback>AI</AvatarFallback>
            </Avatar>
            <div className="rounded-lg p-4 bg-muted">
              {renderThinkingIndicator()}
            </div>
          </div>
        </div>
      )}

      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
