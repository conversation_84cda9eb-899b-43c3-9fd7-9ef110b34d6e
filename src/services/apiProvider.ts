import { AIProvider, ProviderResponse, ToolCall } from "@/types";
import AuthService from "./auth";

interface ProviderConfig {
  apiKey: string;
  baseUrl?: string;
  model?: string;
  maxTokens?: number;
  temperature?: number;
}

abstract class BaseProvider {
  protected config: ProviderConfig;
  protected provider: AIProvider;

  constructor(config: ProviderConfig, provider: AIProvider) {
    this.config = config;
    this.provider = provider;
  }

  abstract sendMessage(
    messages: Array<{ role: string; content: string }>,
    options?: {
      stream?: boolean;
      tools?: any[];
      onStream?: (chunk: string) => void;
      onToolCall?: (toolCall: ToolCall) => void;
    },
  ): Promise<ProviderResponse>;

  protected async makeRequest(
    url: string,
    options: RequestInit,
  ): Promise<Response> {
    const response = await fetch(url, {
      ...options,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${this.config.apiKey}`,
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(
        `API request failed: ${response.status} ${response.statusText}`,
      );
    }

    return response;
  }
}

class OpenAIProvider extends BaseProvider {
  async sendMessage(
    messages: Array<{ role: string; content: string }>,
    options: {
      stream?: boolean;
      tools?: any[];
      onStream?: (chunk: string) => void;
      onToolCall?: (toolCall: ToolCall) => void;
    } = {},
  ): Promise<ProviderResponse> {
    const url = `${this.config.baseUrl || "https://api.openai.com/v1"}/chat/completions`;

    const payload = {
      model: this.config.model || "gpt-4",
      messages,
      max_tokens: this.config.maxTokens || 2000,
      temperature: this.config.temperature || 0.7,
      stream: options.stream || false,
      tools: options.tools,
    };

    if (options.stream) {
      return this.handleStreamingResponse(url, payload, options.onStream);
    } else {
      return this.handleRegularResponse(url, payload);
    }
  }

  private async handleStreamingResponse(
    url: string,
    payload: any,
    onStream?: (chunk: string) => void,
  ): Promise<ProviderResponse> {
    const response = await this.makeRequest(url, {
      method: "POST",
      body: JSON.stringify(payload),
    });

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("No response body");
    }

    let content = "";
    const decoder = new TextDecoder();

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split("\n").filter((line) => line.trim());

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            const data = line.slice(6);
            if (data === "[DONE]") continue;

            try {
              const parsed = JSON.parse(data);
              const delta = parsed.choices?.[0]?.delta?.content;
              if (delta) {
                content += delta;
                onStream?.(delta);
              }
            } catch (error) {
              console.error("Error parsing streaming response:", error);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    return {
      content,
      isComplete: true,
    };
  }

  private async handleRegularResponse(
    url: string,
    payload: any,
  ): Promise<ProviderResponse> {
    const response = await this.makeRequest(url, {
      method: "POST",
      body: JSON.stringify(payload),
    });

    const data = await response.json();

    return {
      content: data.choices?.[0]?.message?.content || "",
      isComplete: true,
      usage: {
        promptTokens: data.usage?.prompt_tokens || 0,
        completionTokens: data.usage?.completion_tokens || 0,
        totalTokens: data.usage?.total_tokens || 0,
      },
    };
  }
}

class ClaudeProvider extends BaseProvider {
  async sendMessage(
    messages: Array<{ role: string; content: string }>,
    options: {
      stream?: boolean;
      tools?: any[];
      onStream?: (chunk: string) => void;
      onToolCall?: (toolCall: ToolCall) => void;
    } = {},
  ): Promise<ProviderResponse> {
    const url = `${this.config.baseUrl || "https://api.anthropic.com/v1"}/messages`;

    const payload = {
      model: this.config.model || "claude-3-sonnet-20240229",
      messages,
      max_tokens: this.config.maxTokens || 2000,
      temperature: this.config.temperature || 0.7,
      stream: options.stream || false,
    };

    const response = await this.makeRequest(url, {
      method: "POST",
      body: JSON.stringify(payload),
      headers: {
        "anthropic-version": "2023-06-01",
      },
    });

    if (options.stream) {
      return this.handleStreamingResponse(response, options.onStream);
    } else {
      const data = await response.json();
      return {
        content: data.content?.[0]?.text || "",
        isComplete: true,
        usage: {
          promptTokens: data.usage?.input_tokens || 0,
          completionTokens: data.usage?.output_tokens || 0,
          totalTokens:
            (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0),
        },
      };
    }
  }

  private async handleStreamingResponse(
    response: Response,
    onStream?: (chunk: string) => void,
  ): Promise<ProviderResponse> {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("No response body");
    }

    let content = "";
    const decoder = new TextDecoder();

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split("\n").filter((line) => line.trim());

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            const data = line.slice(6);
            try {
              const parsed = JSON.parse(data);
              if (parsed.type === "content_block_delta") {
                const delta = parsed.delta?.text;
                if (delta) {
                  content += delta;
                  onStream?.(delta);
                }
              }
            } catch (error) {
              console.error("Error parsing Claude streaming response:", error);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    return {
      content,
      isComplete: true,
    };
  }
}

// Similar implementations for other providers...
class GeminiProvider extends BaseProvider {
  async sendMessage(
    messages: Array<{ role: string; content: string }>,
    options: any = {},
  ): Promise<ProviderResponse> {
    // Gemini implementation
    const url = `${this.config.baseUrl || "https://generativelanguage.googleapis.com/v1beta"}/models/gemini-pro:generateContent`;

    const payload = {
      contents: messages.map((msg) => ({
        parts: [{ text: msg.content }],
        role: msg.role === "assistant" ? "model" : "user",
      })),
      generationConfig: {
        maxOutputTokens: this.config.maxTokens || 2000,
        temperature: this.config.temperature || 0.7,
      },
    };

    const response = await fetch(`${url}?key=${this.config.apiKey}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`Gemini API request failed: ${response.status}`);
    }

    const data = await response.json();

    return {
      content: data.candidates?.[0]?.content?.parts?.[0]?.text || "",
      isComplete: true,
    };
  }
}

class APIProviderService {
  private static instance: APIProviderService;
  private providers: Map<string, BaseProvider> = new Map();
  private providerConfigs: Map<string, AIProvider> = new Map();

  static getInstance(): APIProviderService {
    if (!APIProviderService.instance) {
      APIProviderService.instance = new APIProviderService();
    }
    return APIProviderService.instance;
  }

  constructor() {
    this.initializeProviders();
  }

  private initializeProviders() {
    this.providerConfigs.set("openai", {
      id: "openai",
      name: "OpenAI",
      baseUrl: "https://api.openai.com/v1",
      supportsStreaming: true,
      supportsToolCalls: true,
      maxTokens: 4000,
      costPerToken: 0.00002,
    });

    this.providerConfigs.set("claude", {
      id: "claude",
      name: "Claude",
      baseUrl: "https://api.anthropic.com/v1",
      supportsStreaming: true,
      supportsToolCalls: false,
      maxTokens: 4000,
      costPerToken: 0.000015,
    });

    this.providerConfigs.set("gemini", {
      id: "gemini",
      name: "Gemini",
      baseUrl: "https://generativelanguage.googleapis.com/v1beta",
      supportsStreaming: false,
      supportsToolCalls: false,
      maxTokens: 2048,
      costPerToken: 0.00001,
    });
  }

  async initializeProvider(providerId: string, apiKey: string): Promise<void> {
    const config = this.providerConfigs.get(providerId);
    if (!config) {
      throw new Error(`Unknown provider: ${providerId}`);
    }

    let provider: BaseProvider;

    switch (providerId) {
      case "openai":
        provider = new OpenAIProvider({ apiKey }, config);
        break;
      case "claude":
        provider = new ClaudeProvider({ apiKey }, config);
        break;
      case "gemini":
        provider = new GeminiProvider({ apiKey }, config);
        break;
      default:
        throw new Error(`Provider ${providerId} not implemented`);
    }

    this.providers.set(providerId, provider);
  }

  getProvider(providerId: string): BaseProvider | null {
    return this.providers.get(providerId) || null;
  }

  getAvailableProviders(): AIProvider[] {
    return Array.from(this.providerConfigs.values());
  }

  async sendMessage(
    providerId: string,
    messages: Array<{ role: string; content: string }>,
    options?: any,
  ): Promise<ProviderResponse> {
    const provider = this.getProvider(providerId);
    if (!provider) {
      throw new Error(`Provider ${providerId} not initialized`);
    }

    return provider.sendMessage(messages, options);
  }
}

export default APIProviderService.getInstance();
