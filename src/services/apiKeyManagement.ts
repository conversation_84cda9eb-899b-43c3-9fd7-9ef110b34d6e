import { <PERSON><PERSON><PERSON><PERSON> } from "@/types";
import AuthService from "./auth";
import EncryptionService from "./encryption";

interface CreateApiKeyRequest {
  provider: "openai" | "claude" | "gemini" | "mistral" | "groq";
  name: string;
  key: string;
}

interface UpdateApiKeyRequest {
  name?: string;
  key?: string;
  status?: "active" | "inactive";
}

class ApiKeyManagementService {
  private static instance: ApiKeyManagementService;
  private readonly API_BASE_URL =
    import.meta.env.VITE_API_BASE_URL || "http://localhost:3001";

  static getInstance(): ApiKeyManagementService {
    if (!ApiKeyManagementService.instance) {
      ApiKeyManagementService.instance = new ApiKeyManagementService();
    }
    return ApiKeyManagementService.instance;
  }

  async getApiKeys(): Promise<ApiKey[]> {
    try {
      AuthService.requireAdmin();

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/api-keys`,
      );

      if (response.ok) {
        const data = await response.json();
        return data.apiKeys || [];
      } else {
        throw new Error("Failed to fetch API keys");
      }
    } catch (error) {
      console.error("Error fetching API keys:", error);
      throw error;
    }
  }

  async createApiKey(keyData: CreateApiKeyRequest): Promise<ApiKey> {
    try {
      AuthService.requireAdmin();

      // Validate API key format
      if (!EncryptionService.validateApiKey(keyData.key)) {
        throw new Error("Invalid API key format");
      }

      // Encrypt the API key before sending
      const encryptedKey = EncryptionService.encrypt(keyData.key);

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/api-keys`,
        {
          method: "POST",
          body: JSON.stringify({
            ...keyData,
            encryptedKey,
          }),
        },
      );

      if (response.ok) {
        const newApiKey = await response.json();
        return newApiKey;
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to create API key");
      }
    } catch (error) {
      console.error("Error creating API key:", error);
      throw error;
    }
  }

  async updateApiKey(
    keyId: string,
    updates: UpdateApiKeyRequest,
  ): Promise<ApiKey> {
    try {
      AuthService.requireAdmin();

      const updateData = { ...updates };

      // Encrypt new key if provided
      if (updates.key) {
        if (!EncryptionService.validateApiKey(updates.key)) {
          throw new Error("Invalid API key format");
        }
        updateData.encryptedKey = EncryptionService.encrypt(updates.key);
        delete updateData.key;
      }

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/api-keys/${keyId}`,
        {
          method: "PUT",
          body: JSON.stringify(updateData),
        },
      );

      if (response.ok) {
        const updatedApiKey = await response.json();
        return updatedApiKey;
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to update API key");
      }
    } catch (error) {
      console.error("Error updating API key:", error);
      throw error;
    }
  }

  async deleteApiKey(keyId: string): Promise<void> {
    try {
      AuthService.requireAdmin();

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/api-keys/${keyId}`,
        {
          method: "DELETE",
        },
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to delete API key");
      }
    } catch (error) {
      console.error("Error deleting API key:", error);
      throw error;
    }
  }

  async toggleApiKeyStatus(keyId: string): Promise<ApiKey> {
    try {
      AuthService.requireAdmin();

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/api-keys/${keyId}/toggle-status`,
        {
          method: "PATCH",
        },
      );

      if (response.ok) {
        const updatedApiKey = await response.json();
        return updatedApiKey;
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to toggle API key status");
      }
    } catch (error) {
      console.error("Error toggling API key status:", error);
      throw error;
    }
  }

  async testApiKey(
    keyId: string,
  ): Promise<{ success: boolean; message: string; responseTime?: number }> {
    try {
      AuthService.requireAdmin();

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/api-keys/${keyId}/test`,
        {
          method: "POST",
        },
      );

      if (response.ok) {
        const result = await response.json();
        return result;
      } else {
        const error = await response.json();
        return {
          success: false,
          message: error.message || "API key test failed",
        };
      }
    } catch (error) {
      console.error("Error testing API key:", error);
      return {
        success: false,
        message: "Network error during API key test",
      };
    }
  }

  async getApiKeyUsage(
    keyId: string,
    timeframe: "24h" | "7d" | "30d" = "24h",
  ): Promise<{
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    totalCost: number;
  }> {
    try {
      AuthService.requireAdmin();

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/api-keys/${keyId}/usage?timeframe=${timeframe}`,
      );

      if (response.ok) {
        const usage = await response.json();
        return usage;
      } else {
        throw new Error("Failed to fetch API key usage");
      }
    } catch (error) {
      console.error("Error fetching API key usage:", error);
      throw error;
    }
  }

  getDecryptedKey(encryptedKey: string): string {
    try {
      return EncryptionService.decrypt(encryptedKey);
    } catch (error) {
      console.error("Error decrypting API key:", error);
      throw new Error("Failed to decrypt API key");
    }
  }

  getMaskedKey(key: string): string {
    return EncryptionService.maskApiKey(key);
  }
}

export default ApiKeyManagementService.getInstance();
