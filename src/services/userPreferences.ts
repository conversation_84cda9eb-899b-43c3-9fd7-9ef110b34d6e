import { UserPreferences } from "@/types";
import AuthService from "./auth";

interface UpdateUserPreferencesRequest {
  defaultProvider?: "openai" | "claude" | "gemini" | "mistral" | "groq";
  enableThinkingIndicators?: boolean;
  enableCodeHighlighting?: boolean;
  enableNotifications?: boolean;
  theme?: "light" | "dark" | "system";
  language?: string;
  systemPromptId?: string;
}

class UserPreferencesService {
  private static instance: UserPreferencesService;
  private readonly API_BASE_URL =
    import.meta.env.VITE_API_BASE_URL || "http://localhost:3001";
  private cachedPreferences: UserPreferences | null = null;

  static getInstance(): UserPreferencesService {
    if (!UserPreferencesService.instance) {
      UserPreferencesService.instance = new UserPreferencesService();
    }
    return UserPreferencesService.instance;
  }

  async getUserPreferences(): Promise<UserPreferences> {
    try {
      AuthService.requireAuth();

      // Return cached preferences if available
      if (this.cachedPreferences) {
        return this.cachedPreferences;
      }

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/user-preferences`,
      );

      if (response.ok) {
        const data = await response.json();
        this.cachedPreferences = data.preferences;
        return data.preferences;
      } else if (response.status === 404) {
        // Create default preferences if none exist
        return this.createDefaultPreferences();
      } else {
        throw new Error("Failed to fetch user preferences");
      }
    } catch (error) {
      console.error("Error fetching user preferences:", error);
      // Return default preferences on error
      return this.getDefaultPreferences();
    }
  }

  async updateUserPreferences(
    updates: UpdateUserPreferencesRequest,
  ): Promise<UserPreferences> {
    try {
      AuthService.requireAuth();

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/user-preferences`,
        {
          method: "PUT",
          body: JSON.stringify(updates),
        },
      );

      if (response.ok) {
        const data = await response.json();
        this.cachedPreferences = data.preferences;
        return data.preferences;
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to update user preferences");
      }
    } catch (error) {
      console.error("Error updating user preferences:", error);
      throw error;
    }
  }

  private async createDefaultPreferences(): Promise<UserPreferences> {
    try {
      const defaultPrefs = this.getDefaultPreferences();
      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/user-preferences`,
        {
          method: "POST",
          body: JSON.stringify({
            defaultProvider: defaultPrefs.defaultProvider,
            enableThinkingIndicators: defaultPrefs.enableThinkingIndicators,
            enableCodeHighlighting: defaultPrefs.enableCodeHighlighting,
            enableNotifications: defaultPrefs.enableNotifications,
            theme: defaultPrefs.theme,
            language: defaultPrefs.language,
          }),
        },
      );

      if (response.ok) {
        const data = await response.json();
        this.cachedPreferences = data.preferences;
        return data.preferences;
      } else {
        return defaultPrefs;
      }
    } catch (error) {
      console.error("Error creating default preferences:", error);
      return this.getDefaultPreferences();
    }
  }

  private getDefaultPreferences(): UserPreferences {
    const user = AuthService.getUser();
    return {
      id: "default",
      userId: user?.id || "unknown",
      defaultProvider: "openai",
      enableThinkingIndicators: true,
      enableCodeHighlighting: true,
      enableNotifications: true,
      theme: "system",
      language: "en",
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  // Clear cached preferences (useful after logout)
  clearCache(): void {
    this.cachedPreferences = null;
  }

  // Get specific preference value with fallback
  async getPreference<K extends keyof UserPreferences>(
    key: K,
  ): Promise<UserPreferences[K]> {
    try {
      const preferences = await this.getUserPreferences();
      return preferences[key];
    } catch (error) {
      console.error(`Error getting preference ${String(key)}:`, error);
      const defaultPrefs = this.getDefaultPreferences();
      return defaultPrefs[key];
    }
  }

  // Update a single preference
  async updatePreference<K extends keyof UpdateUserPreferencesRequest>(
    key: K,
    value: UpdateUserPreferencesRequest[K],
  ): Promise<UserPreferences> {
    return this.updateUserPreferences({
      [key]: value,
    } as UpdateUserPreferencesRequest);
  }
}

export default UserPreferencesService.getInstance();
