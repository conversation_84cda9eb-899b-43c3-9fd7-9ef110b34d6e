// Polyfills for older browsers or environments where certain features might not be available

// Ensure Array exists and has the from method
if (typeof Array === 'undefined') {
  console.error('Array is not defined in this environment');
} else if (!Array.from) {
  Array.from = function <T, U>(
    arrayLike: ArrayLike<T> | Iterable<T>,
    mapFn?: (v: T, k: number) => U,
    thisArg?: any
  ): U[] {
    const C = this;
    const items = Object(arrayLike);

    if (arrayLike == null) {
      throw new TypeError('Array.from requires an array-like object - not null or undefined');
    }

    const mapFunction = mapFn === undefined ? undefined : mapFn;
    if (typeof mapFunction !== 'undefined' && typeof mapFunction !== 'function') {
      throw new TypeError('Array.from: when provided, the second argument must be a function');
    }

    const len = parseInt(items.length) || 0;
    const result = typeof C === 'function' ? Object(new C(len)) : new Array(len);

    let k = 0;
    let kValue;
    while (k < len) {
      kValue = items[k];
      if (mapFunction) {
        result[k] = typeof thisArg === 'undefined' ? mapFunction(kValue, k) : mapFunction.call(thisArg, kValue, k);
      } else {
        result[k] = kValue;
      }
      k += 1;
    }

    result.length = len;
    return result;
  };
}

// Object.assign polyfill
if (typeof Object.assign !== 'function') {
  Object.assign = function (target: any, ...sources: any[]) {
    if (target == null) {
      throw new TypeError('Cannot convert undefined or null to object');
    }

    const to = Object(target);

    for (let index = 0; index < sources.length; index++) {
      const nextSource = sources[index];

      if (nextSource != null) {
        for (const nextKey in nextSource) {
          if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
            to[nextKey] = nextSource[nextKey];
          }
        }
      }
    }

    return to;
  };
}

// Promise polyfill check (basic)
if (typeof Promise === 'undefined') {
  console.error('Promise is not available. Please include a Promise polyfill.');
}

// Map polyfill check
if (typeof Map === 'undefined') {
  console.error('Map is not available. Please include a Map polyfill.');
}

// Set polyfill check
if (typeof Set === 'undefined') {
  console.error('Set is not available. Please include a Set polyfill.');
}

// Debug function to check if polyfills are working
export function checkPolyfills() {
  const checks = {
    Array: typeof Array !== 'undefined',
    ArrayFrom: typeof Array !== 'undefined' && typeof Array.from === 'function',
    ObjectAssign: typeof Object.assign === 'function',
    Promise: typeof Promise !== 'undefined',
    Map: typeof Map !== 'undefined',
    Set: typeof Set !== 'undefined'
  };

  console.log('Polyfill status:', checks);

  // Test Array.from specifically
  if (checks.ArrayFrom) {
    try {
      const testArray = Array.from({ length: 3 });
      console.log('Array.from test successful:', testArray);
    } catch (error) {
      console.error('Array.from test failed:', error);
    }
  }

  return checks;
}

export { };
