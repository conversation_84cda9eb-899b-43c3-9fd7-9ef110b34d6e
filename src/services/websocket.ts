import { io, Socket } from "socket.io-client";
import { APIPEvent, ConnectionStatus, WebSocketMessage } from "@/types";
import AuthService from "./auth";

class WebSocketService {
  private static instance: WebSocketService;
  private socket: Socket | null = null;
  private connectionStatus: ConnectionStatus = {
    status: "disconnected",
    reconnectAttempts: 0,
  };
  private listeners: Map<string, ((data: any) => void)[]> = new Map();
  private statusListeners: ((status: ConnectionStatus) => void)[] = [];
  private reconnectTimer: NodeJS.Timeout | null = null;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }

  async connect(): Promise<void> {
    if (this.socket?.connected) {
      return;
    }

    // Validate and refresh token if needed
    const token = await this.getValidToken();
    if (!token) {
      console.error("Cannot connect WebSocket: No valid authentication token");
      this.updateConnectionStatus("disconnected");
      return;
    }

    this.updateConnectionStatus("connecting");

    const serverUrl = import.meta.env.VITE_WS_URL || "http://localhost:3001";

    this.socket = io(serverUrl, {
      auth: {
        token,
      },
      transports: ["websocket", "polling"],
      timeout: 20000,
      reconnection: false, // We'll handle reconnection manually
      forceNew: true, // Force new connection to ensure fresh auth
    });

    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    if (!this.socket) return;

    this.socket.on("connect", () => {
      console.log("WebSocket connected");
      this.updateConnectionStatus("connected");
      this.connectionStatus.reconnectAttempts = 0;
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }
    });

    this.socket.on("disconnect", (reason) => {
      console.log("WebSocket disconnected:", reason);
      this.updateConnectionStatus("disconnected");

      // Auto-reconnect unless it was a manual disconnect
      if (reason !== "io client disconnect") {
        this.scheduleReconnect();
      }
    });

    this.socket.on("connect_error", (error) => {
      console.error("WebSocket connection error:", error);

      // Check if error is due to authentication
      if (
        error.message?.includes("Authentication") ||
        error.message?.includes("token")
      ) {
        console.log("Authentication error detected, attempting token refresh");
        this.handleAuthenticationError();
      } else {
        this.updateConnectionStatus("disconnected");
        this.scheduleReconnect();
      }
    });

    // APIX Protocol Events
    this.socket.on("apix:message", (data: APIPEvent) => {
      this.emit("message", data);
    });

    this.socket.on("apix:thinking", (data: APIPEvent) => {
      this.emit("thinking", data);
    });

    this.socket.on("apix:form_request", (data: APIPEvent) => {
      this.emit("form_request", data);
    });

    this.socket.on("apix:tool_call", (data: APIPEvent) => {
      this.emit("tool_call", data);
    });

    this.socket.on("apix:error", (data: APIPEvent) => {
      this.emit("error", data);
    });

    this.socket.on("apix:stream", (data: APIPEvent) => {
      this.emit("stream", data);
    });

    this.socket.on("apix:complete", (data: APIPEvent) => {
      this.emit("complete", data);
    });
  }

  private scheduleReconnect(): void {
    if (this.connectionStatus.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error("Max reconnection attempts reached");
      this.updateConnectionStatus("disconnected");
      return;
    }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    this.updateConnectionStatus("reconnecting");

    const delay = Math.min(
      this.reconnectDelay *
        Math.pow(2, this.connectionStatus.reconnectAttempts),
      30000, // Max 30 seconds
    );

    this.reconnectTimer = setTimeout(async () => {
      this.connectionStatus.reconnectAttempts++;
      console.log(
        `Attempting to reconnect (${this.connectionStatus.reconnectAttempts}/${this.maxReconnectAttempts})`,
      );
      await this.connect();
    }, delay);
  }

  private updateConnectionStatus(status: ConnectionStatus["status"]): void {
    this.connectionStatus = {
      ...this.connectionStatus,
      status,
      lastConnected:
        status === "connected"
          ? new Date()
          : this.connectionStatus.lastConnected,
    };
    this.statusListeners.forEach((listener) => listener(this.connectionStatus));
  }

  disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    this.updateConnectionStatus("disconnected");
  }

  async send(event: string, data: any): Promise<void> {
    if (!this.socket?.connected) {
      console.error("Cannot send message: WebSocket not connected");
      // Attempt to reconnect if not connected
      await this.connect();
      if (!this.socket?.connected) {
        throw new Error("Failed to establish WebSocket connection");
      }
    }

    // Ensure we have a valid token before sending
    const token = await this.getValidToken();
    if (!token) {
      throw new Error("No valid authentication token available");
    }

    const message: WebSocketMessage = {
      type: event,
      payload: {
        ...data,
        auth: { token }, // Include fresh token with each message
      },
      timestamp: Date.now(),
    };

    this.socket.emit(event, message);
  }

  // APIX Protocol Methods
  async sendMessage(
    conversationId: string,
    content: string,
    options?: { provider?: string },
  ): Promise<void> {
    try {
      await this.send("apix:send_message", {
        conversationId,
        content,
        provider: options?.provider || "openai",
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Failed to send message:", error);
      throw error;
    }
  }

  async submitForm(
    conversationId: string,
    formId: string,
    formData: Record<string, any>,
  ): Promise<void> {
    try {
      await this.send("apix:submit_form", {
        conversationId,
        formId,
        formData,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Failed to submit form:", error);
      throw error;
    }
  }

  async cancelRequest(
    conversationId: string,
    messageId?: string,
  ): Promise<void> {
    try {
      await this.send("apix:cancel", {
        conversationId,
        messageId,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Failed to cancel request:", error);
      throw error;
    }
  }

  // Event Management
  on(event: string, callback: (data: any) => void): () => void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);

    // Return unsubscribe function
    return () => {
      const callbacks = this.listeners.get(event);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }

  private emit(event: string, data: any): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach((callback) => {
        try {
          callback(data);
        } catch (error) {
          console.error(
            `Error in WebSocket event handler for ${event}:`,
            error,
          );
        }
      });
    }
  }

  onConnectionStatusChange(
    callback: (status: ConnectionStatus) => void,
  ): () => void {
    this.statusListeners.push(callback);
    return () => {
      const index = this.statusListeners.indexOf(callback);
      if (index > -1) {
        this.statusListeners.splice(index, 1);
      }
    };
  }

  getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus };
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // Token management methods
  private async getValidToken(): Promise<string | null> {
    const token = AuthService.getToken();
    if (!token) {
      return null;
    }

    // Check if token is still valid
    if (this.isTokenExpiringSoon(token)) {
      console.log("Token expiring soon, attempting refresh");
      const refreshed = await AuthService.refreshToken();
      if (refreshed) {
        return AuthService.getToken();
      } else {
        console.error("Failed to refresh token");
        return null;
      }
    }

    return token;
  }

  private isTokenExpiringSoon(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split(".")[1]));
      const expirationTime = payload.exp * 1000;
      const currentTime = Date.now();
      const timeUntilExpiry = expirationTime - currentTime;

      // Refresh if token expires within 5 minutes
      return timeUntilExpiry < 5 * 60 * 1000;
    } catch (error) {
      console.error("Error checking token expiration:", error);
      return true; // Assume expired if we can't parse
    }
  }

  private async handleAuthenticationError(): Promise<void> {
    console.log("Handling authentication error");

    // Try to refresh the token
    const refreshed = await AuthService.refreshToken();
    if (refreshed) {
      console.log("Token refreshed successfully, attempting reconnection");
      // Disconnect current socket and reconnect with new token
      if (this.socket) {
        this.socket.disconnect();
        this.socket = null;
      }
      this.connectionStatus.reconnectAttempts = 0; // Reset attempts
      await this.connect();
    } else {
      console.error("Failed to refresh token, user needs to re-authenticate");
      this.updateConnectionStatus("disconnected");
      // Emit authentication error event for UI to handle
      this.emit("auth_error", {
        message: "Authentication failed, please log in again",
      });
    }
  }

  // Method to manually refresh connection with new token
  async refreshConnection(): Promise<void> {
    console.log("Manually refreshing WebSocket connection");

    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    this.connectionStatus.reconnectAttempts = 0;
    await this.connect();
  }
}

export default WebSocketService.getInstance();
