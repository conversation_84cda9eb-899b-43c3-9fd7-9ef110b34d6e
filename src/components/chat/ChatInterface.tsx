import React, { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { Send, Loader2, Setting<PERSON>, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Alert, AlertDescription } from "@/components/ui/alert";
import MessageList from "./MessageList";
import DynamicForm from "./DynamicForm";
import WebSocketStatusIndicator from "@/components/WebSocketStatusIndicator";
import WebSocketService from "@/services/websocket";
import AuthService from "@/services/auth";
import RoutingRulesService from "@/services/routingRules";
import UserPreferencesService from "@/services/userPreferences";
import {
  Message,
  DynamicFormData,
  APIPEvent,
  ConnectionStatus,
  UserPreferences,
} from "@/types";

interface ChatInterfaceProps {
  conversationId?: string;
  initialMessages?: Message[];
  onSendMessage?: (message: string) => void;
  onFormSubmit?: (formId: string, data: Record<string, any>) => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  conversationId = "default-conversation",
  initialMessages = [],
  onSendMessage = () => {},
  onFormSubmit = () => {},
}) => {
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [inputValue, setInputValue] = useState("");
  const [isThinking, setIsThinking] = useState(false);
  const [activeForm, setActiveForm] = useState<DynamicFormData | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    status: "disconnected",
    reconnectAttempts: 0,
  });
  const [currentStreamingMessage, setCurrentStreamingMessage] =
    useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [selectedProvider, setSelectedProvider] = useState<string>("openai");
  const [userPreferences, setUserPreferences] =
    useState<UserPreferences | null>(null);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const wsService = WebSocketService;
  const authService = AuthService;
  const routingService = RoutingRulesService;

  // Initialize WebSocket connection and load user preferences
  useEffect(() => {
    const initializeChat = async () => {
      if (authService.isAuthenticated()) {
        wsService.connect();

        // Load user preferences
        try {
          const preferences = await UserPreferencesService.getUserPreferences();
          setUserPreferences(preferences);
          setSelectedProvider(preferences.defaultProvider);
        } catch (error) {
          console.error("Failed to load user preferences:", error);
        }
      }
    };

    initializeChat();

    return () => {
      wsService.disconnect();
    };
  }, []);

  // Set up WebSocket event listeners
  useEffect(() => {
    const unsubscribeStatus = wsService.onConnectionStatusChange((status) => {
      setConnectionStatus(status);

      // Enhanced error handling for connection status
      if (status.status === "disconnected" && status.reconnectAttempts === 0) {
        setError("Connection lost. Please check your internet connection.");
      } else if (status.status === "reconnecting") {
        setError(`Reconnecting... (attempt ${status.reconnectAttempts}/5)`);
      } else if (status.status === "connected") {
        setError(null);
      }
    });

    const unsubscribeMessage = wsService.on("message", (event: APIPEvent) => {
      if (event.conversationId === conversationId) {
        handleMessageEvent(event);
      }
    });

    const unsubscribeThinking = wsService.on("thinking", (event: APIPEvent) => {
      if (event.conversationId === conversationId) {
        setIsThinking(event.data.isThinking);
      }
    });

    const unsubscribeStream = wsService.on("stream", (event: APIPEvent) => {
      if (event.conversationId === conversationId) {
        handleStreamEvent(event);
      }
    });

    const unsubscribeComplete = wsService.on("complete", (event: APIPEvent) => {
      if (event.conversationId === conversationId) {
        handleCompleteEvent(event);
      }
    });

    const unsubscribeFormRequest = wsService.on(
      "form_request",
      (event: APIPEvent) => {
        if (event.conversationId === conversationId) {
          setActiveForm(event.data.form);
        }
      },
    );

    const unsubscribeError = wsService.on("error", (event: APIPEvent) => {
      if (event.conversationId === conversationId) {
        const errorMessage =
          event.data.message ||
          "An error occurred while processing your request";
        setError(errorMessage);
        setIsThinking(false);
        setCurrentStreamingMessage("");

        // Show user-friendly error messages
        if (errorMessage.includes("rate limit")) {
          setError(
            "Rate limit exceeded. Please wait a moment before sending another message.",
          );
        } else if (errorMessage.includes("authentication")) {
          setError(
            "Authentication error. Please refresh the page and try again.",
          );
        } else if (errorMessage.includes("network")) {
          setError(
            "Network error. Please check your connection and try again.",
          );
        }
      }
    });

    const unsubscribeAuthError = wsService.on("auth_error", (event: any) => {
      setError("Authentication expired. Please refresh the page to continue.");
      setIsThinking(false);
      setCurrentStreamingMessage("");
    });

    return () => {
      unsubscribeStatus();
      unsubscribeMessage();
      unsubscribeThinking();
      unsubscribeStream();
      unsubscribeComplete();
      unsubscribeFormRequest();
      unsubscribeError();
      unsubscribeAuthError();
    };
  }, [conversationId]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, isThinking, currentStreamingMessage]);

  // Focus input when component mounts
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  // Handle incoming message events
  const handleMessageEvent = (event: APIPEvent) => {
    const message: Message = {
      id: event.messageId || `msg-${Date.now()}`,
      conversationId: conversationId,
      role: event.data.role || "assistant",
      content: event.data.content,
      timestamp: new Date(event.data.timestamp),
      status: "complete",
      provider: event.data.provider,
      toolCalls: event.data.toolCalls,
    };

    setMessages((prev) => {
      const existingIndex = prev.findIndex((m) => m.id === message.id);
      if (existingIndex >= 0) {
        const updated = [...prev];
        updated[existingIndex] = message;
        return updated;
      }
      return [...prev, message];
    });
    setIsThinking(false);
    setCurrentStreamingMessage("");
  };

  // Handle streaming events
  const handleStreamEvent = (event: APIPEvent) => {
    if (event.data.chunk) {
      setCurrentStreamingMessage((prev) => prev + event.data.chunk);
    }
  };

  // Handle completion events
  const handleCompleteEvent = (event: APIPEvent) => {
    const finalMessage: Message = {
      id: event.messageId || `msg-${Date.now()}`,
      conversationId: conversationId,
      role: "assistant",
      content: currentStreamingMessage || event.data.content,
      timestamp: new Date(),
      status: "complete",
      provider: event.data.provider,
    };

    setMessages((prev) => [...prev, finalMessage]);
    setCurrentStreamingMessage("");
    setIsThinking(false);
  };

  const handleSendMessage = () => {
    if (!inputValue.trim()) {
      setError("Please enter a message");
      return;
    }

    if (!wsService.isConnected()) {
      setError("Not connected to server. Please check your connection.");
      return;
    }

    const messageContent = inputValue.trim();

    try {
      // Determine the appropriate provider based on routing rules or user preference
      let provider = routingService.evaluateMessage(messageContent);

      // If no routing rule matches and we have user preferences, use their default
      if (provider === "openai" && userPreferences?.defaultProvider) {
        // Only override if the routing service returned the default (no specific rule matched)
        const hasSpecificRule = routingService
          .getActiveRules()
          .some(
            (rule) =>
              rule.condition !== "default" &&
              routingService.evaluateMessage(messageContent) === rule.provider,
          );
        if (!hasSpecificRule) {
          provider = userPreferences.defaultProvider;
        }
      }

      setSelectedProvider(provider);

      const newMessage: Message = {
        id: `msg-${Date.now()}`,
        conversationId: conversationId,
        role: "user",
        content: messageContent,
        timestamp: new Date(),
        status: "complete",
        provider: provider,
      };

      setMessages((prev) => [...prev, newMessage]);
      setInputValue("");
      setError(null);
      setIsThinking(true);

      // Send message via WebSocket using APIX protocol with provider selection
      wsService.sendMessage(conversationId, messageContent, { provider });
      onSendMessage(messageContent);
    } catch (error: any) {
      console.error("Error sending message:", error);
      setError(error.message || "Failed to send message");
    }
  };

  const handleFormSubmit = (data: Record<string, any>) => {
    if (!activeForm || !wsService.isConnected()) return;

    // Send form data via WebSocket
    wsService.submitForm(conversationId, activeForm.id, data);
    onFormSubmit(activeForm.id, data);

    // Add the form submission as a user message
    const formDataString = Object.entries(data)
      .map(([key, value]) => `${key}: ${value}`)
      .join("\n");

    const newMessage: Message = {
      id: `msg-${Date.now()}`,
      conversationId: conversationId,
      role: "user",
      content: `Form submitted:\n${formDataString}`,
      timestamp: new Date(),
      status: "complete",
    };

    setMessages((prev) => [...prev, newMessage]);
    setActiveForm(null);
    setIsThinking(true);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleReconnect = () => {
    wsService.refreshConnection();
  };

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-2">
          <Avatar>
            <AvatarImage
              src="https://api.dicebear.com/7.x/avataaars/svg?seed=ai-assistant"
              alt="AI"
            />
            <AvatarFallback>AI</AvatarFallback>
          </Avatar>
          <div>
            <h2 className="text-lg font-semibold">AI Assistant</h2>
            <div className="flex items-center text-sm text-muted-foreground gap-2">
              <WebSocketStatusIndicator
                variant="icon"
                size="sm"
                onReconnect={handleReconnect}
              />
              {selectedProvider && (
                <span className="px-2 py-1 bg-primary/10 text-primary rounded-md text-xs font-medium">
                  {selectedProvider.toUpperCase()}
                </span>
              )}
            </div>
          </div>
        </div>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon">
                <Settings className="h-5 w-5" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Settings</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive" className="mx-4 mt-4">
          <AlertDescription>{error}</AlertDescription>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setError(null)}
            className="ml-auto"
          >
            <X className="h-4 w-4" />
          </Button>
        </Alert>
      )}

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <MessageList
          messages={messages}
          isThinking={
            isThinking && (userPreferences?.enableThinkingIndicators ?? true)
          }
          streamingContent={currentStreamingMessage}
        />
        <div ref={messagesEndRef} />
      </div>

      {/* Dynamic Form (if active) */}
      {activeForm && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 border-t"
        >
          <Card>
            <CardContent className="pt-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">{activeForm.title}</h3>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setActiveForm(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              {activeForm.description && (
                <p className="text-sm text-muted-foreground mb-4">
                  {activeForm.description}
                </p>
              )}
              <DynamicForm
                formId={activeForm.id}
                title={activeForm.title}
                description={activeForm.description}
                fields={activeForm.fields}
                onSubmit={handleFormSubmit}
                onCancel={() => setActiveForm(null)}
              />
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Input Area */}
      <div className="p-4 border-t">
        <div className="flex space-x-2">
          <Input
            ref={inputRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Type your message..."
            disabled={isThinking || !!activeForm}
            className="flex-1"
          />
          <Button
            onClick={handleSendMessage}
            disabled={
              !inputValue.trim() ||
              isThinking ||
              !!activeForm ||
              !wsService.isConnected()
            }
          >
            {isThinking ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
        <div className="mt-2 text-xs text-muted-foreground flex justify-between items-center">
          <p>Press Enter to send, Shift+Enter for new line</p>
          <div className="flex items-center gap-2">
            {connectionStatus.status !== "connected" && (
              <p className="text-red-500">
                Connection required to send messages
              </p>
            )}
            <WebSocketStatusIndicator
              variant="badge"
              size="sm"
              showText={false}
              onReconnect={handleReconnect}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
