export interface User {
  id: string;
  email: string;
  name: string;
  role: "admin" | "user";
  createdAt: Date;
  lastActive: Date;
}

export interface UserWithStatus extends User {
  status: "active" | "inactive";
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface ApiKey {
  id: string;
  provider: "openai" | "claude" | "gemini" | "mistral" | "groq";
  name: string;
  encryptedKey: string;
  lastUsed: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface ApiKeyWithStatus extends ApiKey {
  status: "active" | "inactive";
}

export interface RoutingRule {
  id: string;
  name: string;
  priority: number;
  condition: string;
  provider: "openai" | "claude" | "gemini" | "mistral" | "groq";
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Message {
  id: string;
  conversationId: string;
  role: "user" | "assistant" | "system";
  content: string;
  timestamp: Date;
  status?: "complete" | "streaming" | "thinking" | "error";
  provider?: string;
  toolCalls?: ToolCall[];
}

export interface ToolCall {
  id: string;
  tool: string;
  input: Record<string, any>;
  output?: Record<string, any>;
  status: "pending" | "complete" | "error";
}

export interface Conversation {
  id: string;
  userId: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  messages: Message[];
}

export interface APIPEvent {
  type:
    | "message"
    | "thinking"
    | "form_request"
    | "tool_call"
    | "error"
    | "connection";
  data: any;
  conversationId?: string;
  messageId?: string;
}

export interface FormField {
  id: string;
  type: "text" | "textarea" | "select" | "checkbox" | "radio";
  label: string;
  placeholder?: string;
  required?: boolean;
  options?: { value: string; label: string }[];
  description?: string;
  defaultValue?: string | boolean;
  validation?: {
    pattern?: string;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
  };
}

export interface DynamicFormData {
  id: string;
  title: string;
  description?: string;
  fields: FormField[];
}

export interface SystemPrompt {
  id: string;
  name: string;
  content: string;
  context: "general" | "technical" | "creative" | "support" | "custom";
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface UserPreferences {
  id: string;
  userId: string;
  defaultProvider: "openai" | "claude" | "gemini" | "mistral" | "groq";
  enableThinkingIndicators: boolean;
  enableCodeHighlighting: boolean;
  enableNotifications: boolean;
  theme: "light" | "dark" | "system";
  language: string;
  systemPromptId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface AIProvider {
  id: string;
  name: string;
  baseUrl: string;
  supportsStreaming: boolean;
  supportsToolCalls: boolean;
  maxTokens: number;
  costPerToken: number;
}

export interface ProviderResponse {
  content: string;
  isComplete: boolean;
  toolCalls?: ToolCall[];
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: number;
}

export interface ConnectionStatus {
  status: "connected" | "disconnected" | "connecting" | "reconnecting";
  lastConnected?: Date;
  reconnectAttempts: number;
}

export interface WebSocketEvent {
  type: string;
  payload: any;
}

export interface APIPEvent {
  type:
    | "message"
    | "thinking"
    | "form_request"
    | "tool_call"
    | "error"
    | "connection";
  data: any;
}