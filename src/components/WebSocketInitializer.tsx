import { useEffect } from "react";
import WebSocketService from "@/services/websocket";
import AuthService from "@/services/auth";
import RoutingRulesService from "@/services/routingRules";
import APIProviderService from "@/services/apiProvider";

const WebSocketInitializer: React.FC = () => {
  useEffect(() => {
    const initializeServices = async () => {
      try {
        // Initialize authentication state
        const authState = AuthService.getAuthState();

        if (authState.isAuthenticated) {
          // Initialize WebSocket connection
          WebSocketService.connect();

          // Load routing rules
          await RoutingRulesService.loadRules();

          // Initialize API providers with available keys
          // This would typically load encrypted API keys from the backend
          console.log("Services initialized successfully");
        }
      } catch (error) {
        console.error("Failed to initialize services:", error);
      }
    };

    initializeServices();

    // Cleanup on unmount
    return () => {
      WebSocketService.disconnect();
    };
  }, []);

  // This component doesn't render anything visible
  return null;
};

export default WebSocketInitializer;
