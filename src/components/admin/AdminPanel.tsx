import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  PlusCircle,
  Trash2,
  Edit,
  BarChart3,
  Key,
  Settings,
  Users,
  AlertTriangle,
  RefreshCw,
  TestTube,
  Eye,
  EyeOff,
  CheckCircle,
  XCircle,
} from "lucide-react";
import AuthService from "@/services/auth";
import RoutingRulesService from "@/services/routingRules";
import ApiKeyManagementService from "@/services/apiKeyManagement";
import UserManagementService from "@/services/userManagement";
import {
  ApiKey as ApiKeyType,
  RoutingRule as RoutingRuleType,
  SystemPrompt,
  ApiKeyWithStatus,
} from "@/types";
import SystemPromptsService from "@/services/systemPrompts";
import { Textarea } from "@/components/ui/textarea";
import { TestTube2 } from "lucide-react";
import WebSocketStatusIndicator from "@/components/WebSocketStatusIndicator";

interface ApiKey {
  id: string;
  provider: string;
  name: string;
  key: string;
  status: "active" | "inactive";
  lastUsed: string;
}

interface RoutingRule {
  id: string;
  name: string;
  priority: number;
  condition: string;
  provider: string;
  isActive: boolean;
}

interface PerformanceMetric {
  provider: string;
  responseTime: number;
  successRate: number;
  costPerQuery: number;
  totalQueries: number;
}

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  status: "active" | "inactive";
  lastActive: string;
}

const AdminPanel = () => {
  // Real data state
  const [apiKeys, setApiKeys] = useState<ApiKeyWithStatus[]>([]);
  const [routingRules, setRoutingRules] = useState<RoutingRuleType[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<
    PerformanceMetric[]
  >([]);
  const [users, setUsers] = useState<User[]>([]);
  const [systemPrompts, setSystemPrompts] = useState<SystemPrompt[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for dialogs and forms
  const [apiKeyDialogOpen, setApiKeyDialogOpen] = useState(false);
  const [routingRuleDialogOpen, setRoutingRuleDialogOpen] = useState(false);
  const [userDialogOpen, setUserDialogOpen] = useState(false);
  const [systemPromptDialogOpen, setSystemPromptDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [selectedItemType, setSelectedItemType] = useState<
    "apiKey" | "routingRule" | "user" | "systemPrompt" | null
  >(null);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [showApiKey, setShowApiKey] = useState<Record<string, boolean>>({});
  const [testingApiKey, setTestingApiKey] = useState<string | null>(null);
  const [testingSystemPrompt, setTestingSystemPrompt] = useState<string | null>(
    null,
  );
  const [testResults, setTestResults] = useState<Record<string, any>>({});

  // Form states
  const [apiKeyForm, setApiKeyForm] = useState({
    provider: "",
    name: "",
    key: "",
  });
  const [routingRuleForm, setRoutingRuleForm] = useState({
    name: "",
    priority: 1,
    condition: "",
    provider: "",
  });
  const [userForm, setUserForm] = useState({
    name: "",
    email: "",
    password: "",
    role: "user" as "admin" | "user",
  });
  const [systemPromptForm, setSystemPromptForm] = useState({
    name: "",
    content: "",
    context: "general" as
      | "general"
      | "technical"
      | "creative"
      | "support"
      | "custom",
  });

  // Load data on component mount
  useEffect(() => {
    loadAllData();
  }, []);

  const loadAllData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check if user is admin
      if (!AuthService.isAdmin()) {
        setError("Admin privileges required to access this panel");
        return;
      }

      const [apiKeysData, routingRulesData, usersData, systemPromptsData] =
        await Promise.all([
          ApiKeyManagementService.getApiKeys().catch((err) => {
            console.error("Failed to load API keys:", err);
            return [];
          }),
          RoutingRulesService.getRules(),
          UserManagementService.getUsers().catch((err) => {
            console.error("Failed to load users:", err);
            return [];
          }),
          SystemPromptsService.getSystemPrompts().catch((err) => {
            console.error("Failed to load system prompts:", err);
            return [];
          }),
        ]);

      setApiKeys(apiKeysData as ApiKeyWithStatus[]);
      setRoutingRules(routingRulesData);
      setUsers(usersData as User[]);
      setSystemPrompts(systemPromptsData);

      // Load performance metrics (mock for now, would be real API call)
      setPerformanceMetrics([
        {
          provider: "OpenAI",
          responseTime: 1.2,
          successRate: 99.5,
          costPerQuery: 0.02,
          totalQueries: 15420,
        },
        {
          provider: "Claude",
          responseTime: 1.8,
          successRate: 98.7,
          costPerQuery: 0.015,
          totalQueries: 8750,
        },
        {
          provider: "Gemini",
          responseTime: 1.5,
          successRate: 97.8,
          costPerQuery: 0.01,
          totalQueries: 6230,
        },
        {
          provider: "Mistral",
          responseTime: 1.3,
          successRate: 98.2,
          costPerQuery: 0.008,
          totalQueries: 4120,
        },
        {
          provider: "Groq",
          responseTime: 0.9,
          successRate: 96.5,
          costPerQuery: 0.005,
          totalQueries: 2340,
        },
      ]);
    } catch (error) {
      console.error("Error loading admin data:", error);
      setError("Failed to load admin panel data");
    } finally {
      setLoading(false);
    }
  };

  // API Key handlers
  const handleAddApiKey = async () => {
    try {
      if (!apiKeyForm.provider || !apiKeyForm.name || !apiKeyForm.key) {
        setError("All fields are required");
        return;
      }

      const newApiKey = await ApiKeyManagementService.createApiKey({
        provider: apiKeyForm.provider as any,
        name: apiKeyForm.name,
        key: apiKeyForm.key,
      });

      setApiKeys((prev) => [...prev, newApiKey as ApiKeyWithStatus]);
      setApiKeyDialogOpen(false);
      setApiKeyForm({ provider: "", name: "", key: "" });
      setError(null);
    } catch (error: any) {
      setError(error.message || "Failed to create API key");
    }
  };

  const handleEditApiKey = async () => {
    try {
      if (!editingItem || !apiKeyForm.name) {
        setError("Name is required");
        return;
      }

      const updates: any = { name: apiKeyForm.name };
      if (apiKeyForm.key) {
        updates.key = apiKeyForm.key;
      }

      const updatedApiKey = await ApiKeyManagementService.updateApiKey(
        editingItem.id,
        updates,
      );

      setApiKeys((prev) =>
        prev.map((key) =>
          key.id === editingItem.id ? (updatedApiKey as ApiKeyWithStatus) : key,
        ),
      );
      setApiKeyDialogOpen(false);
      setEditingItem(null);
      setApiKeyForm({ provider: "", name: "", key: "" });
      setError(null);
    } catch (error: any) {
      setError(error.message || "Failed to update API key");
    }
  };

  const handleTestApiKey = async (keyId: string) => {
    try {
      setTestingApiKey(keyId);
      const result = await ApiKeyManagementService.testApiKey(keyId);
      setTestResults((prev) => ({ ...prev, [keyId]: result }));
    } catch (error: any) {
      setTestResults((prev) => ({
        ...prev,
        [keyId]: { success: false, message: error.message },
      }));
    } finally {
      setTestingApiKey(null);
    }
  };

  // Routing Rule handlers
  const handleAddRoutingRule = async () => {
    try {
      if (
        !routingRuleForm.name ||
        !routingRuleForm.condition ||
        !routingRuleForm.provider
      ) {
        setError("All fields are required");
        return;
      }

      const newRule = await RoutingRulesService.createRule({
        name: routingRuleForm.name,
        priority: routingRuleForm.priority,
        condition: routingRuleForm.condition,
        provider: routingRuleForm.provider as any,
        isActive: true,
      });

      setRoutingRules((prev) => [...prev, newRule]);
      setRoutingRuleDialogOpen(false);
      setRoutingRuleForm({
        name: "",
        priority: 1,
        condition: "",
        provider: "",
      });
      setError(null);
    } catch (error: any) {
      setError(error.message || "Failed to create routing rule");
    }
  };

  const handleEditRoutingRule = async () => {
    try {
      if (
        !editingItem ||
        !routingRuleForm.name ||
        !routingRuleForm.condition ||
        !routingRuleForm.provider
      ) {
        setError("All fields are required");
        return;
      }

      const updatedRule = await RoutingRulesService.updateRule(editingItem.id, {
        name: routingRuleForm.name,
        priority: routingRuleForm.priority,
        condition: routingRuleForm.condition,
        provider: routingRuleForm.provider as any,
      });

      setRoutingRules((prev) =>
        prev.map((rule) => (rule.id === editingItem.id ? updatedRule : rule)),
      );
      setRoutingRuleDialogOpen(false);
      setEditingItem(null);
      setRoutingRuleForm({
        name: "",
        priority: 1,
        condition: "",
        provider: "",
      });
      setError(null);
    } catch (error: any) {
      setError(error.message || "Failed to update routing rule");
    }
  };

  const handleTestRoutingRule = async (ruleId: string) => {
    const testMessage = prompt(
      "Enter a test message to evaluate against this rule:",
    );
    if (!testMessage) return;

    try {
      const result = await RoutingRulesService.testRule(ruleId, testMessage);
      alert(
        `Test Result:\nMatches: ${result.matches}\nProvider: ${result.provider}`,
      );
    } catch (error: any) {
      alert(`Test failed: ${error.message}`);
    }
  };

  // User handlers
  const handleAddUser = async () => {
    try {
      if (!userForm.name || !userForm.email || !userForm.password) {
        setError("All fields are required");
        return;
      }

      const newUser = await UserManagementService.createUser({
        name: userForm.name,
        email: userForm.email,
        password: userForm.password,
        role: userForm.role,
      });

      setUsers((prev) => [...prev, newUser as unknown as User]);
      setUserDialogOpen(false);
      setUserForm({ name: "", email: "", password: "", role: "user" });
      setError(null);
    } catch (error: any) {
      setError(error.message || "Failed to create user");
    }
  };

  const handleEditUser = async () => {
    try {
      if (!editingItem || !userForm.name || !userForm.email) {
        setError("Name and email are required");
        return;
      }

      const updates: any = {
        name: userForm.name,
        email: userForm.email,
        role: userForm.role,
      };

      const updatedUser = await UserManagementService.updateUser(
        editingItem.id,
        updates,
      );

      setUsers((prev) =>
        prev.map((user) =>
          user.id === editingItem.id ? (updatedUser as unknown as User) : user,
        ),
      );
      setUserDialogOpen(false);
      setEditingItem(null);
      setUserForm({ name: "", email: "", password: "", role: "user" });
      setError(null);
    } catch (error: any) {
      setError(error.message || "Failed to update user");
    }
  };

  // System Prompt handlers
  const handleAddSystemPrompt = async () => {
    try {
      if (
        !systemPromptForm.name ||
        !systemPromptForm.content ||
        !systemPromptForm.context
      ) {
        setError("All fields are required");
        return;
      }

      const newPrompt = await SystemPromptsService.createSystemPrompt({
        name: systemPromptForm.name,
        content: systemPromptForm.content,
        context: systemPromptForm.context,
      });

      setSystemPrompts((prev) => [...prev, newPrompt]);
      setSystemPromptDialogOpen(false);
      setSystemPromptForm({
        name: "",
        content: "",
        context: "general",
      });
      setError(null);
    } catch (error: any) {
      setError(error.message || "Failed to create system prompt");
    }
  };

  const handleEditSystemPrompt = async () => {
    try {
      if (
        !editingItem ||
        !systemPromptForm.name ||
        !systemPromptForm.content ||
        !systemPromptForm.context
      ) {
        setError("All fields are required");
        return;
      }

      const updatedPrompt = await SystemPromptsService.updateSystemPrompt(
        editingItem.id,
        {
          name: systemPromptForm.name,
          content: systemPromptForm.content,
          context: systemPromptForm.context,
        },
      );

      setSystemPrompts((prev) =>
        prev.map((prompt) =>
          prompt.id === editingItem.id ? updatedPrompt : prompt,
        ),
      );
      setSystemPromptDialogOpen(false);
      setEditingItem(null);
      setSystemPromptForm({
        name: "",
        content: "",
        context: "general",
      });
      setError(null);
    } catch (error: any) {
      setError(error.message || "Failed to update system prompt");
    }
  };

  const handleTestSystemPrompt = async (promptId: string) => {
    const testMessage = prompt(
      "Enter a test message to evaluate with this system prompt:",
    );
    if (!testMessage) return;

    try {
      setTestingSystemPrompt(promptId);
      const result = await SystemPromptsService.testSystemPrompt(
        promptId,
        testMessage,
      );
      setTestResults((prev) => ({ ...prev, [promptId]: result }));

      if (result.success) {
        alert(`Test Result:\n\nResponse: ${result.response}`);
      } else {
        alert(`Test failed: ${result.error}`);
      }
    } catch (error: any) {
      alert(`Test failed: ${error.message}`);
    } finally {
      setTestingSystemPrompt(null);
    }
  };

  // Delete handler
  const handleDeleteItem = async () => {
    try {
      if (!selectedItemId || !selectedItemType) return;

      switch (selectedItemType) {
        case "apiKey":
          await ApiKeyManagementService.deleteApiKey(selectedItemId);
          setApiKeys((prev) => prev.filter((key) => key.id !== selectedItemId));
          break;
        case "routingRule":
          await RoutingRulesService.deleteRule(selectedItemId);
          setRoutingRules((prev) =>
            prev.filter((rule) => rule.id !== selectedItemId),
          );
          break;
        case "user":
          await UserManagementService.deleteUser(selectedItemId);
          setUsers((prev) => prev.filter((user) => user.id !== selectedItemId));
          break;
        case "systemPrompt":
          await SystemPromptsService.deleteSystemPrompt(selectedItemId);
          setSystemPrompts((prev) =>
            prev.filter((prompt) => prompt.id !== selectedItemId),
          );
          break;
      }

      setDeleteDialogOpen(false);
      setSelectedItemId(null);
      setSelectedItemType(null);
      setError(null);
    } catch (error: any) {
      setError(error.message || "Failed to delete item");
    }
  };

  // Toggle status handler
  const handleToggleStatus = async (
    id: string,
    type: "apiKey" | "routingRule" | "user" | "systemPrompt",
  ) => {
    try {
      switch (type) {
        case "apiKey":
          const updatedApiKey =
            await ApiKeyManagementService.toggleApiKeyStatus(id);
          setApiKeys((prev) =>
            prev.map((key) =>
              key.id === id ? (updatedApiKey as ApiKeyWithStatus) : key,
            ),
          );
          break;
        case "routingRule":
          const rule = routingRules.find((r) => r.id === id);
          if (rule) {
            const updatedRule = await RoutingRulesService.updateRule(id, {
              isActive: !rule.isActive,
            });
            setRoutingRules((prev) =>
              prev.map((r) => (r.id === id ? updatedRule : r)),
            );
          }
          break;
        case "user":
          const updatedUser = await UserManagementService.toggleUserStatus(id);
          setUsers((prev) =>
            prev.map((user) =>
              user.id === id ? (updatedUser as unknown as User) : user,
            ),
          );
          break;
        case "systemPrompt":
          const updatedPrompt =
            await SystemPromptsService.toggleSystemPromptStatus(id);
          setSystemPrompts((prev) =>
            prev.map((prompt) => (prompt.id === id ? updatedPrompt : prompt)),
          );
          break;
      }
      setError(null);
    } catch (error: any) {
      setError(error.message || "Failed to toggle status");
    }
  };

  // Helper functions
  const openEditDialog = (
    item: any,
    type: "apiKey" | "routingRule" | "user" | "systemPrompt",
  ) => {
    setEditingItem(item);

    switch (type) {
      case "apiKey":
        setApiKeyForm({ provider: item.provider, name: item.name, key: "" });
        setApiKeyDialogOpen(true);
        break;
      case "routingRule":
        setRoutingRuleForm({
          name: item.name,
          priority: item.priority,
          condition: item.condition,
          provider: item.provider,
        });
        setRoutingRuleDialogOpen(true);
        break;
      case "user":
        setUserForm({
          name: item.name,
          email: item.email,
          password: "",
          role: item.role,
        });
        setUserDialogOpen(true);
        break;
      case "systemPrompt":
        setSystemPromptForm({
          name: item.name,
          content: item.content,
          context: item.context,
        });
        setSystemPromptDialogOpen(true);
        break;
    }
  };

  const openDeleteDialog = (
    id: string,
    type: "apiKey" | "routingRule" | "user" | "systemPrompt",
  ) => {
    setSelectedItemId(id);
    setSelectedItemType(type);
    setDeleteDialogOpen(true);
  };

  const toggleApiKeyVisibility = (keyId: string) => {
    setShowApiKey((prev) => ({ ...prev, [keyId]: !prev[keyId] }));
  };

  if (loading) {
    return (
      <div className="bg-background min-h-screen p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading admin panel...</p>
        </div>
      </div>
    );
  }

  if (error && !AuthService.isAdmin()) {
    return (
      <div className="bg-background min-h-screen p-6 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="text-muted-foreground">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background min-h-screen p-6">
      <div className="container mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">Admin Panel</h1>
          <WebSocketStatusIndicator variant="badge" />
        </div>

        <Tabs defaultValue="api-keys" className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="api-keys" className="flex items-center gap-2">
              <Key size={16} />
              API Keys
            </TabsTrigger>
            <TabsTrigger value="routing" className="flex items-center gap-2">
              <Settings size={16} />
              Routing Rules
            </TabsTrigger>
            <TabsTrigger
              value="performance"
              className="flex items-center gap-2"
            >
              <BarChart3 size={16} />
              Performance
            </TabsTrigger>
            <TabsTrigger value="users" className="flex items-center gap-2">
              <Users size={16} />
              User Management
            </TabsTrigger>
            <TabsTrigger
              value="system-prompts"
              className="flex items-center gap-2"
            >
              <Settings size={16} />
              System Prompts
            </TabsTrigger>
          </TabsList>

          {/* API Keys Tab */}
          <TabsContent value="api-keys">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>API Keys</CardTitle>
                  <CardDescription>
                    Manage API keys for different AI providers
                  </CardDescription>
                </div>
                <Dialog
                  open={apiKeyDialogOpen}
                  onOpenChange={setApiKeyDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button className="flex items-center gap-2">
                      <PlusCircle size={16} />
                      Add API Key
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>
                        {editingItem ? "Edit API Key" : "Add New API Key"}
                      </DialogTitle>
                      <DialogDescription>
                        {editingItem
                          ? "Update the API key details."
                          : "Enter the details for the new API key. The key will be encrypted before storage."}
                      </DialogDescription>
                    </DialogHeader>
                    {error && (
                      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                        {error}
                      </div>
                    )}
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="name" className="text-right">
                          Name
                        </Label>
                        <Input
                          id="name"
                          placeholder="Production Key"
                          className="col-span-3"
                          value={apiKeyForm.name}
                          onChange={(e) =>
                            setApiKeyForm((prev) => ({
                              ...prev,
                              name: e.target.value,
                            }))
                          }
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="provider" className="text-right">
                          Provider
                        </Label>
                        <Select
                          value={apiKeyForm.provider}
                          onValueChange={(value) =>
                            setApiKeyForm((prev) => ({
                              ...prev,
                              provider: value,
                            }))
                          }
                          disabled={!!editingItem}
                        >
                          <SelectTrigger className="col-span-3">
                            <SelectValue placeholder="Select provider" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="openai">OpenAI</SelectItem>
                            <SelectItem value="claude">Claude</SelectItem>
                            <SelectItem value="gemini">Gemini</SelectItem>
                            <SelectItem value="mistral">Mistral</SelectItem>
                            <SelectItem value="groq">Groq</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="api-key" className="text-right">
                          API Key
                        </Label>
                        <Input
                          id="api-key"
                          type="password"
                          placeholder={
                            editingItem
                              ? "Leave empty to keep current key"
                              : "sk-..."
                          }
                          className="col-span-3"
                          value={apiKeyForm.key}
                          onChange={(e) =>
                            setApiKeyForm((prev) => ({
                              ...prev,
                              key: e.target.value,
                            }))
                          }
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setApiKeyDialogOpen(false);
                          setEditingItem(null);
                          setApiKeyForm({ provider: "", name: "", key: "" });
                          setError(null);
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={
                          editingItem ? handleEditApiKey : handleAddApiKey
                        }
                      >
                        {editingItem ? "Update Key" : "Add Key"}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Provider</TableHead>
                      <TableHead>Key</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Last Used</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {apiKeys.map((key) => (
                      <TableRow key={key.id}>
                        <TableCell className="font-medium">
                          {key.name}
                        </TableCell>
                        <TableCell>{key.provider}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <span className="font-mono text-sm">
                              {showApiKey[key.id]
                                ? key.encryptedKey
                                : ApiKeyManagementService.getMaskedKey(
                                    key.encryptedKey,
                                  )}
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={() => toggleApiKeyVisibility(key.id)}
                            >
                              {showApiKey[key.id] ? (
                                <EyeOff size={12} />
                              ) : (
                                <Eye size={12} />
                              )}
                            </Button>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Badge
                              variant={
                                key.status === "active"
                                  ? "default"
                                  : "secondary"
                              }
                            >
                              {key.status}
                            </Badge>
                            {testResults[key.id] && (
                              <div className="flex items-center gap-1">
                                {testResults[key.id].success ? (
                                  <CheckCircle className="h-4 w-4 text-green-500" />
                                ) : (
                                  <XCircle className="h-4 w-4 text-red-500" />
                                )}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {key.lastUsed
                            ? new Date(key.lastUsed).toLocaleString()
                            : "Never"}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => openEditDialog(key, "apiKey")}
                            >
                              <Edit size={16} />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleTestApiKey(key.id)}
                              disabled={testingApiKey === key.id}
                            >
                              {testingApiKey === key.id ? (
                                <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                              ) : (
                                <TestTube size={16} />
                              )}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-destructive"
                              onClick={() => openDeleteDialog(key.id, "apiKey")}
                            >
                              <Trash2 size={16} />
                            </Button>
                            <Switch
                              checked={key.status === "active"}
                              onCheckedChange={() =>
                                handleToggleStatus(key.id, "apiKey")
                              }
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Routing Rules Tab */}
          <TabsContent value="routing">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Routing Rules</CardTitle>
                  <CardDescription>
                    Configure how queries are routed to different AI providers
                  </CardDescription>
                </div>
                <Dialog
                  open={routingRuleDialogOpen}
                  onOpenChange={setRoutingRuleDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button className="flex items-center gap-2">
                      <PlusCircle size={16} />
                      Add Rule
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>
                        {editingItem ? "Edit Routing Rule" : "Add Routing Rule"}
                      </DialogTitle>
                      <DialogDescription>
                        {editingItem
                          ? "Update the routing rule details."
                          : "Create a new rule to route specific queries to a particular AI provider."}
                      </DialogDescription>
                    </DialogHeader>
                    {error && (
                      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                        {error}
                      </div>
                    )}
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="rule-name" className="text-right">
                          Name
                        </Label>
                        <Input
                          id="rule-name"
                          placeholder="Technical Queries"
                          className="col-span-3"
                          value={routingRuleForm.name}
                          onChange={(e) =>
                            setRoutingRuleForm((prev) => ({
                              ...prev,
                              name: e.target.value,
                            }))
                          }
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="priority" className="text-right">
                          Priority
                        </Label>
                        <Input
                          id="priority"
                          type="number"
                          placeholder="1"
                          className="col-span-3"
                          value={routingRuleForm.priority}
                          onChange={(e) =>
                            setRoutingRuleForm((prev) => ({
                              ...prev,
                              priority: parseInt(e.target.value) || 1,
                            }))
                          }
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="condition" className="text-right">
                          Condition
                        </Label>
                        <Input
                          id="condition"
                          placeholder='contains("code", "programming")'
                          className="col-span-3"
                          value={routingRuleForm.condition}
                          onChange={(e) =>
                            setRoutingRuleForm((prev) => ({
                              ...prev,
                              condition: e.target.value,
                            }))
                          }
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="rule-provider" className="text-right">
                          Provider
                        </Label>
                        <Select
                          value={routingRuleForm.provider}
                          onValueChange={(value) =>
                            setRoutingRuleForm((prev) => ({
                              ...prev,
                              provider: value,
                            }))
                          }
                        >
                          <SelectTrigger className="col-span-3">
                            <SelectValue placeholder="Select provider" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="openai">OpenAI</SelectItem>
                            <SelectItem value="claude">Claude</SelectItem>
                            <SelectItem value="gemini">Gemini</SelectItem>
                            <SelectItem value="mistral">Mistral</SelectItem>
                            <SelectItem value="groq">Groq</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setRoutingRuleDialogOpen(false);
                          setEditingItem(null);
                          setRoutingRuleForm({
                            name: "",
                            priority: 1,
                            condition: "",
                            provider: "",
                          });
                          setError(null);
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={
                          editingItem
                            ? handleEditRoutingRule
                            : handleAddRoutingRule
                        }
                      >
                        {editingItem ? "Update Rule" : "Add Rule"}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Priority</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Condition</TableHead>
                      <TableHead>Provider</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {routingRules.map((rule) => (
                      <TableRow key={rule.id}>
                        <TableCell>{rule.priority}</TableCell>
                        <TableCell className="font-medium">
                          {rule.name}
                        </TableCell>
                        <TableCell className="max-w-md truncate">
                          {rule.condition}
                        </TableCell>
                        <TableCell>{rule.provider}</TableCell>
                        <TableCell>
                          <Badge
                            variant={rule.isActive ? "default" : "secondary"}
                          >
                            {rule.isActive ? "active" : "inactive"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() =>
                                openEditDialog(rule, "routingRule")
                              }
                            >
                              <Edit size={16} />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleTestRoutingRule(rule.id)}
                            >
                              <TestTube size={16} />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-destructive"
                              onClick={() =>
                                openDeleteDialog(rule.id, "routingRule")
                              }
                            >
                              <Trash2 size={16} />
                            </Button>
                            <Switch
                              checked={rule.isActive}
                              onCheckedChange={() =>
                                handleToggleStatus(rule.id, "routingRule")
                              }
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Performance Tab */}
          <TabsContent value="performance">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>System Performance</CardTitle>
                    <CardDescription>
                      Monitor the performance of different AI providers
                    </CardDescription>
                  </div>
                  <Button variant="outline" className="flex items-center gap-2">
                    <RefreshCw size={16} />
                    Refresh Data
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-medium mb-4">
                      Response Time (seconds)
                    </h3>
                    <div className="space-y-4">
                      {performanceMetrics.map((metric) => (
                        <div
                          key={`rt-${metric.provider}`}
                          className="space-y-1"
                        >
                          <div className="flex items-center justify-between">
                            <span>{metric.provider}</span>
                            <span className="text-sm text-muted-foreground">
                              {metric.responseTime}s
                            </span>
                          </div>
                          <Progress
                            value={100 - (metric.responseTime / 2) * 100}
                          />
                        </div>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-lg font-medium mb-4">
                      Success Rate (%)
                    </h3>
                    <div className="space-y-4">
                      {performanceMetrics.map((metric) => (
                        <div
                          key={`sr-${metric.provider}`}
                          className="space-y-1"
                        >
                          <div className="flex items-center justify-between">
                            <span>{metric.provider}</span>
                            <span className="text-sm text-muted-foreground">
                              {metric.successRate}%
                            </span>
                          </div>
                          <Progress value={metric.successRate} />
                        </div>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-lg font-medium mb-4">
                      Cost Per Query ($)
                    </h3>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Provider</TableHead>
                          <TableHead>Cost Per Query</TableHead>
                          <TableHead>Total Queries</TableHead>
                          <TableHead>Estimated Total Cost</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {performanceMetrics.map((metric) => (
                          <TableRow key={`cost-${metric.provider}`}>
                            <TableCell>{metric.provider}</TableCell>
                            <TableCell>
                              ${metric.costPerQuery.toFixed(4)}
                            </TableCell>
                            <TableCell>
                              {metric.totalQueries.toLocaleString()}
                            </TableCell>
                            <TableCell>
                              $
                              {(
                                metric.costPerQuery * metric.totalQueries
                              ).toFixed(2)}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  <div className="bg-muted/50 p-4 rounded-lg flex items-center gap-3">
                    <AlertTriangle className="text-amber-500" />
                    <p className="text-sm">
                      Performance data is updated every 15 minutes. Last update:
                      2023-06-15 16:30:00
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* User Management Tab */}
          <TabsContent value="users">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>User Management</CardTitle>
                  <CardDescription>
                    Manage user access and permissions
                  </CardDescription>
                </div>
                <Dialog open={userDialogOpen} onOpenChange={setUserDialogOpen}>
                  <DialogTrigger asChild>
                    <Button className="flex items-center gap-2">
                      <PlusCircle size={16} />
                      Add User
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>
                        {editingItem ? "Edit User" : "Add New User"}
                      </DialogTitle>
                      <DialogDescription>
                        {editingItem
                          ? "Update the user details."
                          : "Create a new user account with specified role and permissions."}
                      </DialogDescription>
                    </DialogHeader>
                    {error && (
                      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                        {error}
                      </div>
                    )}
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="user-name" className="text-right">
                          Name
                        </Label>
                        <Input
                          id="user-name"
                          placeholder="John Doe"
                          className="col-span-3"
                          value={userForm.name}
                          onChange={(e) =>
                            setUserForm((prev) => ({
                              ...prev,
                              name: e.target.value,
                            }))
                          }
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="user-email" className="text-right">
                          Email
                        </Label>
                        <Input
                          id="user-email"
                          type="email"
                          placeholder="<EMAIL>"
                          className="col-span-3"
                          value={userForm.email}
                          onChange={(e) =>
                            setUserForm((prev) => ({
                              ...prev,
                              email: e.target.value,
                            }))
                          }
                        />
                      </div>
                      {!editingItem && (
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="user-password" className="text-right">
                            Password
                          </Label>
                          <Input
                            id="user-password"
                            type="password"
                            placeholder="••••••••"
                            className="col-span-3"
                            value={userForm.password}
                            onChange={(e) =>
                              setUserForm((prev) => ({
                                ...prev,
                                password: e.target.value,
                              }))
                            }
                          />
                        </div>
                      )}
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="user-role" className="text-right">
                          Role
                        </Label>
                        <Select
                          value={userForm.role}
                          onValueChange={(value: "admin" | "user") =>
                            setUserForm((prev) => ({ ...prev, role: value }))
                          }
                        >
                          <SelectTrigger className="col-span-3">
                            <SelectValue placeholder="Select role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="user">User</SelectItem>
                            <SelectItem value="admin">Administrator</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setUserDialogOpen(false);
                          setEditingItem(null);
                          setUserForm({
                            name: "",
                            email: "",
                            password: "",
                            role: "user",
                          });
                          setError(null);
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={editingItem ? handleEditUser : handleAddUser}
                      >
                        {editingItem ? "Update User" : "Add User"}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[500px]">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Last Active</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell className="font-medium">
                            {user.name}
                          </TableCell>
                          <TableCell>{user.email}</TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                user.role === "admin" ? "default" : "secondary"
                              }
                            >
                              {user.role}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                user.status === "active"
                                  ? "default"
                                  : "secondary"
                              }
                            >
                              {user.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {user.lastActive
                              ? new Date(user.lastActive).toLocaleString()
                              : "Never"}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() => openEditDialog(user, "user")}
                              >
                                <Edit size={16} />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 text-destructive"
                                onClick={() =>
                                  openDeleteDialog(user.id, "user")
                                }
                                disabled={AuthService.getUser()?.id === user.id}
                              >
                                <Trash2 size={16} />
                              </Button>
                              <Switch
                                checked={user.status === "active"}
                                onCheckedChange={() =>
                                  handleToggleStatus(user.id, "user")
                                }
                                disabled={AuthService.getUser()?.id === user.id}
                              />
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          {/* System Prompts Tab */}
          <TabsContent value="system-prompts">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>System Prompts</CardTitle>
                  <CardDescription>
                    Manage system prompts that define AI behavior and
                    personality
                  </CardDescription>
                </div>
                <Dialog
                  open={systemPromptDialogOpen}
                  onOpenChange={setSystemPromptDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button className="flex items-center gap-2">
                      <PlusCircle size={16} />
                      Add System Prompt
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>
                        {editingItem
                          ? "Edit System Prompt"
                          : "Add System Prompt"}
                      </DialogTitle>
                      <DialogDescription>
                        {editingItem
                          ? "Update the system prompt details."
                          : "Create a new system prompt to define AI behavior in specific contexts."}
                      </DialogDescription>
                    </DialogHeader>
                    {error && (
                      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                        {error}
                      </div>
                    )}
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="prompt-name" className="text-right">
                          Name
                        </Label>
                        <Input
                          id="prompt-name"
                          placeholder="Creative Writing Assistant"
                          className="col-span-3"
                          value={systemPromptForm.name}
                          onChange={(e) =>
                            setSystemPromptForm((prev) => ({
                              ...prev,
                              name: e.target.value,
                            }))
                          }
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="prompt-context" className="text-right">
                          Context
                        </Label>
                        <Select
                          value={systemPromptForm.context}
                          onValueChange={(value: any) =>
                            setSystemPromptForm((prev) => ({
                              ...prev,
                              context: value,
                            }))
                          }
                        >
                          <SelectTrigger className="col-span-3">
                            <SelectValue placeholder="Select context" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="general">General</SelectItem>
                            <SelectItem value="technical">Technical</SelectItem>
                            <SelectItem value="creative">Creative</SelectItem>
                            <SelectItem value="support">Support</SelectItem>
                            <SelectItem value="custom">Custom</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="grid grid-cols-4 items-start gap-4">
                        <Label
                          htmlFor="prompt-content"
                          className="text-right pt-2"
                        >
                          Content
                        </Label>
                        <Textarea
                          id="prompt-content"
                          placeholder="You are a helpful AI assistant that..."
                          className="col-span-3 min-h-[200px]"
                          value={systemPromptForm.content}
                          onChange={(e) =>
                            setSystemPromptForm((prev) => ({
                              ...prev,
                              content: e.target.value,
                            }))
                          }
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setSystemPromptDialogOpen(false);
                          setEditingItem(null);
                          setSystemPromptForm({
                            name: "",
                            content: "",
                            context: "general",
                          });
                          setError(null);
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={
                          editingItem
                            ? handleEditSystemPrompt
                            : handleAddSystemPrompt
                        }
                      >
                        {editingItem ? "Update Prompt" : "Add Prompt"}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Context</TableHead>
                      <TableHead>Content Preview</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {systemPrompts.map((prompt) => (
                      <TableRow key={prompt.id}>
                        <TableCell className="font-medium">
                          {prompt.name}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{prompt.context}</Badge>
                        </TableCell>
                        <TableCell className="max-w-md">
                          <div className="truncate text-sm text-muted-foreground">
                            {prompt.content.substring(0, 100)}
                            {prompt.content.length > 100 && "..."}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Badge
                              variant={
                                prompt.isActive ? "default" : "secondary"
                              }
                            >
                              {prompt.isActive ? "active" : "inactive"}
                            </Badge>
                            {testResults[prompt.id] && (
                              <div className="flex items-center gap-1">
                                {testResults[prompt.id].success ? (
                                  <CheckCircle className="h-4 w-4 text-green-500" />
                                ) : (
                                  <XCircle className="h-4 w-4 text-red-500" />
                                )}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {new Date(prompt.createdAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() =>
                                openEditDialog(prompt, "systemPrompt")
                              }
                            >
                              <Edit size={16} />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleTestSystemPrompt(prompt.id)}
                              disabled={testingSystemPrompt === prompt.id}
                            >
                              {testingSystemPrompt === prompt.id ? (
                                <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                              ) : (
                                <TestTube2 size={16} />
                              )}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-destructive"
                              onClick={() =>
                                openDeleteDialog(prompt.id, "systemPrompt")
                              }
                            >
                              <Trash2 size={16} />
                            </Button>
                            <Switch
                              checked={prompt.isActive}
                              onCheckedChange={() =>
                                handleToggleStatus(prompt.id, "systemPrompt")
                              }
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Global Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete this{" "}
              {selectedItemType?.replace(/([A-Z])/g, " $1").toLowerCase()}.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-destructive text-destructive-foreground"
              onClick={handleDeleteItem}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default AdminPanel;
