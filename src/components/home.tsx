import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  PlusIcon,
  Settings,
  LogOut,
  MessageSquare,
  Shield,
  Eye,
  EyeOff,
} from "lucide-react";
import ChatInterface from "./chat/ChatInterface";
import AdminPanel from "./admin/AdminPanel";
import UserSettingsPanel from "./UserSettingsPanel";
import WebSocketStatusIndicator from "./WebSocketStatusIndicator";
import AuthService from "@/services/auth";
import { AuthState, User } from "@/types";

const Home = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    token: null,
    isAuthenticated: false,
    isLoading: true,
  });
  const [conversations, setConversations] = useState([
    {
      id: "1",
      title: "Project Planning Discussion",
      timestamp: "2 hours ago",
      preview: "Let's discuss the roadmap for Q3...",
    },
    {
      id: "2",
      title: "Customer Support Query",
      timestamp: "1 day ago",
      preview: "How can I integrate the API with...",
    },
    {
      id: "3",
      title: "Feature Brainstorming",
      timestamp: "3 days ago",
      preview: "I need ideas for improving user engagement...",
    },
  ]);
  const [activeConversation, setActiveConversation] = useState("1");
  const [currentTab, setCurrentTab] = useState("chat");
  const [loginForm, setLoginForm] = useState({
    email: "",
    password: "",
    name: "",
  });
  const [isRegistering, setIsRegistering] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);

  // Subscribe to auth state changes
  useEffect(() => {
    const unsubscribe = AuthService.subscribe((state) => {
      setAuthState(state);
    });

    // Initialize auth state
    setAuthState(AuthService.getAuthState());

    return unsubscribe;
  }, []);

  // Handle new conversation
  const handleNewConversation = () => {
    const newId = (conversations.length + 1).toString();
    const newConversation = {
      id: newId,
      title: "New Conversation",
      timestamp: "Just now",
      preview: "Start a new conversation...",
    };
    setConversations([newConversation, ...conversations]);
    setActiveConversation(newId);
  };

  // Handle authentication
  const handleAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    setAuthError(null);

    try {
      let result;
      if (isRegistering) {
        result = await AuthService.register(
          loginForm.email,
          loginForm.password,
          loginForm.name,
        );
      } else {
        result = await AuthService.login(loginForm.email, loginForm.password);
      }

      if (!result.success) {
        setAuthError(result.error || "Authentication failed");
      }
    } catch (error) {
      setAuthError("An unexpected error occurred");
    }
  };

  // Handle logout
  const handleLogout = async () => {
    await AuthService.logout();
  };

  // Show loading state
  if (authState.isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Login form for unauthenticated users
  if (!authState.isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <Card className="w-[400px]">
          <CardContent className="pt-6">
            <div className="mb-4 text-center">
              <h2 className="text-2xl font-bold">
                {isRegistering ? "Create Account" : "Login to UAUI"}
              </h2>
              <p className="text-muted-foreground">
                {isRegistering
                  ? "Create your account to access the platform"
                  : "Enter your credentials to access the platform"}
              </p>
            </div>

            {authError && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{authError}</AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleAuth} className="space-y-4">
              {isRegistering && (
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    type="text"
                    placeholder="Enter your full name"
                    value={loginForm.name}
                    onChange={(e) =>
                      setLoginForm((prev) => ({
                        ...prev,
                        name: e.target.value,
                      }))
                    }
                    required
                  />
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={loginForm.email}
                  onChange={(e) =>
                    setLoginForm((prev) => ({ ...prev, email: e.target.value }))
                  }
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your password"
                    value={loginForm.password}
                    onChange={(e) =>
                      setLoginForm((prev) => ({
                        ...prev,
                        password: e.target.value,
                      }))
                    }
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={authState.isLoading}
              >
                {authState.isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                    {isRegistering ? "Creating Account..." : "Signing In..."}
                  </>
                ) : isRegistering ? (
                  "Create Account"
                ) : (
                  "Sign In"
                )}
              </Button>
            </form>

            <div className="mt-4 text-center">
              <Button
                variant="link"
                onClick={() => {
                  setIsRegistering(!isRegistering);
                  setAuthError(null);
                  setLoginForm({ email: "", password: "", name: "" });
                }}
              >
                {isRegistering
                  ? "Already have an account? Sign in"
                  : "Don't have an account? Create one"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <div className="w-64 border-r bg-card flex flex-col">
        <div className="p-4 border-b">
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={handleNewConversation}
          >
            <PlusIcon className="mr-2 h-4 w-4" />
            New Conversation
          </Button>
        </div>

        <ScrollArea className="flex-1">
          <div className="p-2 space-y-2">
            {conversations.map((conversation) => (
              <Card
                key={conversation.id}
                className={`cursor-pointer hover:bg-accent ${activeConversation === conversation.id ? "bg-accent" : ""}`}
                onClick={() => setActiveConversation(conversation.id)}
              >
                <CardContent className="p-3">
                  <div className="font-medium truncate">
                    {conversation.title}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {conversation.timestamp}
                  </div>
                  <div className="text-sm truncate mt-1">
                    {conversation.preview}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </ScrollArea>

        <div className="p-4 border-t mt-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Avatar className="h-8 w-8 mr-2">
                <AvatarImage
                  src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${authState.user?.email}`}
                />
                <AvatarFallback>
                  {authState.user?.name?.charAt(0).toUpperCase() || "U"}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="text-sm font-medium">
                  {authState.user?.name}
                </div>
                <div className="text-xs text-muted-foreground">
                  {authState.user?.email}
                </div>
              </div>
            </div>
            <Button variant="ghost" size="icon" onClick={handleLogout}>
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        <div className="border-b p-4 flex justify-between items-center">
          <Tabs
            value={currentTab}
            onValueChange={setCurrentTab}
            className="w-full"
          >
            <TabsList>
              <TabsTrigger value="chat">
                <MessageSquare className="h-4 w-4 mr-2" />
                Chat
              </TabsTrigger>
              <TabsTrigger value="settings">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </TabsTrigger>
              {authState.user?.role === "admin" && (
                <TabsTrigger value="admin">
                  <Shield className="h-4 w-4 mr-2" />
                  Admin
                </TabsTrigger>
              )}
            </TabsList>
          </Tabs>
          <WebSocketStatusIndicator variant="badge" size="sm" />
        </div>

        <div className="flex-1 overflow-hidden">
          <TabsContent value="chat" className="h-full">
            <ChatInterface conversationId={activeConversation} />
          </TabsContent>

          <TabsContent value="settings" className="h-full overflow-auto">
            <UserSettingsPanel />
          </TabsContent>

          {authState.user?.role === "admin" && (
            <TabsContent value="admin" className="h-full">
              <AdminPanel />
            </TabsContent>
          )}
        </div>
      </div>
    </div>
  );
};

export default Home;
